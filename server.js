const express = require('express');
const bodyParser = require('body-parser');
const stripe = require('stripe')('sk_test_51Rt2QjK0cts3LCMejeK6mroZZLkWpFy63QPE07dgcy26QsKTjnpMeTrglJdknjmMr7h6exgrYPYW8WHGJnaMjAy600PN54V7Id');

const app = express();

// Stripe 要求原始body用于签名验证，所以这里用 raw
app.use('/webhook', bodyParser.raw({ type: 'application/json' }));

app.post('/webhook', (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = 'whsec_1aa6fb6ddab7b4772bb2e73d2b76910ecfb8c47693025fefda7337d62d8e7a41';

  let event;
  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('❌ 签名验证失败：', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log(`✅ 收到事件: ${event.type}`);
  console.log(JSON.stringify(event, null, 2));

  res.json({ received: true });
});

app.listen(1718, () => console.log('✅ Webhook 监听在 http://localhost:1718/webhook'));





 if event_type == 'checkout.session.completed':
        print('🔔 Payment succeeded!')
    elif event_type == 'customer.subscription.trial_will_end':
        print('Subscription trial will end')
    elif event_type == 'customer.subscription.created':
        print('Subscription created %s', event.id)
    elif event_type == 'customer.subscription.updated':
        print('Subscription created %s', event.id)
    elif event_type == 'customer.subscription.deleted':
        # handle subscription canceled automatically based
        # upon your subscription settings. Or if the user cancels it.
        print('Subscription canceled: %s', event.id)
    elif event_type == 'entitlements.active_entitlement_summary.updated':
        # handle active entitlement summary updated
        print('Active entitlement summary updated: %s', event.id)

    return jsonify({'status': 'success'})