
'checkout.session.completed':{
  "id": "evt_1RvDUGK0cts3LCMegHPVmJO6",
  "object": "event",
  "api_version": "2025-07-30.basil",
  "created": 1754986996,
  "data": {
    "object": {
      "id": "price_1RvDUGK0cts3LCMergqzyw3V",
      "object": "price",
      "active": true,
      "billing_scheme": "per_unit",
      "created": 1754986996,
      "currency": "usd",
      "custom_unit_amount": null,
      "livemode": false,
      "lookup_key": null,
      "metadata": {},
      "nickname": null,
      "product": "prod_SqvKxUTTKfTCXx",
      "recurring": null,
      "tax_behavior": "unspecified",
      "tiers_mode": null,
      "transform_quantity": null,
      "type": "one_time",
      "unit_amount": 1500,
      "unit_amount_decimal": "1500"
    }
  },
  "livemode": false,
  "pending_webhooks": 2,
  "request": {
    "id": "req_MDGGOaqqBNe2Wv",
    "idempotency_key": "f0752753-9cfd-456b-9c0a-12e3dd23c0fe"
  },
  "type": "price.created"
},

'customer.subscription.trial_will_end':{
  "id": "evt_1RvDWpK0cts3LCMeH5NUAI4X",
  "object": "event",
  "api_version": "2025-07-30.basil",
  "created": **********,
  "data": {
    "object": {
      "id": "in_1RvDWoK0cts3LCMePgDeXOPj",
      "object": "invoice",
      "account_country": "US",
      "account_name": "test 沙盒",
      "account_tax_ids": null,
      "amount_due": 0,
      "amount_overpaid": 0,
      "amount_paid": 0,
      "amount_remaining": 0,
      "amount_shipping": 0,
      "application": null,
      "attempt_count": 0,
      "attempted": true,
      "auto_advance": false,
      "automatic_tax": {
        "disabled_reason": null,
        "enabled": false,
        "liability": null,
        "provider": null,
        "status": null
      },
      "automatically_finalizes_at": null,
      "billing_reason": "subscription_create",
      "collection_method": "charge_automatically",
      "created": **********,
      "currency": "usd",
      "custom_fields": null,
      "customer": "cus_SqvN8ox97furGV",
      "customer_address": null,
      "customer_email": null,
      "customer_name": null,
      "customer_phone": null,
      "customer_shipping": null,
      "customer_tax_exempt": "none",
      "customer_tax_ids": [],
      "default_payment_method": null,
      "default_source": null,
      "default_tax_rates": [],
      "description": null,
      "discounts": [],
      "due_date": null,
      "effective_at": **********,
      "ending_balance": 0,
      "footer": null,
      "from_invoice": null,
      "hosted_invoice_url": "https://invoice.stripe.com/i/acct_1Rt2QjK0cts3LCMe/test_YWNjdF8xUnQyUWpLMGN0czNMQ01lLF9TcXZOTkZyWU81M0JkMTQ2ZWlwbDFNNGN0aDVQSGxRLDE0NTUyNzk1NQ0200efzhnEez?s=ap",
      "invoice_pdf": "https://pay.stripe.com/invoice/acct_1Rt2QjK0cts3LCMe/test_YWNjdF8xUnQyUWpLMGN0czNMQ01lLF9TcXZOTkZyWU81M0JkMTQ2ZWlwbDFNNGN0aDVQSGxRLDE0NTUyNzk1NQ0200efzhnEez/pdf?s=ap",
      "issuer": {
        "type": "self"
      },
      "last_finalization_error": null,
      "latest_revision": null,
      "lines": {
        "object": "list",
        "data": [
          {
            "id": "il_1RvDWoK0cts3LCMevbCtwjzT",
            "object": "line_item",
            "amount": 0,
            "currency": "usd",
            "description": "Trial period for myproduct",
            "discount_amounts": [],
            "discountable": true,
            "discounts": [],
            "invoice": "in_1RvDWoK0cts3LCMePgDeXOPj",
            "livemode": false,
            "metadata": {},
            "parent": {
              "invoice_item_details": null,
              "subscription_item_details": {
                "invoice_item": null,
                "proration": false,
                "proration_details": {
                  "credited_items": null
                },
                "subscription": "sub_1RvDWnK0cts3LCMeqQ7L8CIK",
                "subscription_item": "si_SqvNpBHCPfUHtP"
              },
              "type": "subscription_item_details"
            },
            "period": {
              "end": 1755073553,
              "start": 1754987153
            },
            "pretax_credit_amounts": [],
            "pricing": {
              "price_details": {
                "price": "price_1RvDWmK0cts3LCMe2T0nhHxT",
                "product": "prod_SqvNrmXp9B3qu5"
              },
              "type": "price_details",
              "unit_amount_decimal": "0"
            },
            "quantity": 1,
            "taxes": []
          }
        ],
        "has_more": false,
        "total_count": 1,
        "url": "/v1/invoices/in_1RvDWoK0cts3LCMePgDeXOPj/lines"
      },
      "livemode": false,
      "metadata": {},
      "next_payment_attempt": null,
      "number": "2FZY3ZL6-0001",
      "on_behalf_of": null,
      "parent": {
        "quote_details": null,
        "subscription_details": {
          "metadata": {},
          "subscription": "sub_1RvDWnK0cts3LCMeqQ7L8CIK"
        },
        "type": "subscription_details"
      },
      "payment_settings": {
        "default_mandate": null,
        "payment_method_options": null,
        "payment_method_types": null
      },
      "period_end": 1754987153,
      "period_start": 1754987153,
      "post_payment_credit_notes_amount": 0,
      "pre_payment_credit_notes_amount": 0,
      "receipt_number": null,
      "rendering": null,
      "shipping_cost": null,
      "shipping_details": null,
      "starting_balance": 0,
      "statement_descriptor": null,
      "status": "paid",
      "status_transitions": {
        "finalized_at": **********,
        "marked_uncollectible_at": null,
        "paid_at": 1754987153,
        "voided_at": null
      },
      "subtotal": 0,
      "subtotal_excluding_tax": 0,
      "test_clock": null,
      "total": 0,
      "total_discount_amounts": [],
      "total_excluding_tax": 0,
      "total_pretax_credit_amounts": [],
      "total_taxes": [],
      "webhooks_delivered_at": null
    }
  },
  "livemode": false,
  "pending_webhooks": 2,
  "request": {
    "id": "req_shKWPJurjY3wJi",
    "idempotency_key": "3dd082fb-be9a-4a3b-ba9a-2dbabde4aa71"
  },
  "type": "invoice.payment_succeeded"
}